"""
Stitching Worker <PERSON><PERSON>le
Handles the stitching process using interpolated Z values from Mapping AF
Optimized with asynchronous capture pipeline and RAM Disk staging
"""

import os
import time
import shutil
from datetime import datetime
from queue import Queue, Empty
from threading import Thread, Event
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QImage, QImageWriter


class StitchingWorker(QObject):
    """
    Worker class for handling stitching process
    """
    
    # Signals
    progress = pyqtSignal(int, str)  # progress percentage, status message
    log_message = pyqtSignal(str)    # log messages
    finished = pyqtSignal()          # finished signal
    error_occurred = pyqtSignal(str) # error signal
    
    def __init__(self, camera, grbl, z_interpolation, output_folder=None):
        """
        Initialize StitchingWorker

        Args:
            camera: Camera instance for capturing images
            grbl: GRBL instance for movement control
            z_interpolation: ZInterpolation object with interpolated grid points
            output_folder: Optional custom output folder path
        """
        super().__init__()

        self.camera = camera
        self.grbl = grbl
        self.z_interpolation = z_interpolation
        self.output_folder = output_folder

        # Stitching parameters
        self.current_point = 0
        self.total_points = 0
        self.grid_points = []
        self.is_running = False
        self.should_stop = False
        self.last_known_position = (0, 0, 0)
        self.timing_stats = {}

        # Dual stitching parameters
        self.current_run = 1  # 1 for interpolated Z, 2 for fixed Z
        self.total_runs = 2
        self.center_z_value = None  # Will store the center Z from mapping AF
        self.interpolated_grid_points = []  # Store original interpolated points
        self.fixed_z_grid_points = []  # Store points with fixed Z

        # Extract center Z value from mapping results
        self._extract_center_z_value()
        
        # Movement parameters
        self.movement_feedrate = 1000  # mm/min
        self.z_feedrate = 500         # mm/min for Z movements
        self.settle_time = 0.05       # seconds to wait after movement
        self.capture_delay = 0.01     # seconds to wait before capture

        # Optimized capture parameters
        self.use_ram_disk = self._detect_ram_disk()
        self.ram_disk_root = r"A:\stitching_cache"
        self.staging_format = "BMP"  # BMP for fastest write, PNG_FAST for compressed
        self.final_format = "PNG"    # Final output format

        # Asynchronous capture pipeline
        self._save_queue = None
        self._writer_thread = None
        self._writer_stop_event = None
        self._staging_folder = None
        self._files_staged = []  # Track staged files for final copy

    def _extract_center_z_value(self):
        """
        Extract the center Z value from mapping AF results.
        The center point is the one that used 'full' AF in the 3x3 grid.
        """
        try:
            if not hasattr(self.z_interpolation, 'mapping_results') or not self.z_interpolation.mapping_results:
                self.log_message.emit("⚠️ Warning: No mapping results available for center Z extraction")
                return

            # Calculate center coordinates from grbl_start and grbl_end
            x_start, y_start = self.z_interpolation.grbl_start
            x_end, y_end = self.z_interpolation.grbl_end
            center_x = (x_start + x_end) / 2
            center_y = (y_start + y_end) / 2

            # Look for the center point in mapping results
            center_z = None
            min_distance = float('inf')

            for (x, y), z_value in self.z_interpolation.mapping_results.items():
                distance = ((x - center_x)**2 + (y - center_y)**2)**0.5
                if distance < min_distance and z_value > 0:
                    min_distance = distance
                    center_z = z_value

            if center_z is not None:
                self.center_z_value = center_z
                self.log_message.emit(f"✓ Center Z value extracted: {center_z:.4f} (from full AF at center point)")
            else:
                self.log_message.emit("⚠️ Warning: Could not find valid center Z value from mapping results")

        except Exception as e:
            self.log_message.emit(f"Error extracting center Z value: {e}")

    def _detect_ram_disk(self):
        """
        Detect if RAM Disk (A:) is available for staging

        Returns:
            bool: True if RAM Disk is available and writable
        """
        try:
            ram_disk_path = "A:\\"
            if os.path.exists(ram_disk_path) and os.access(ram_disk_path, os.W_OK):
                # Test write capability
                test_file = os.path.join(ram_disk_path, "test_write.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                return True
        except Exception:
            pass
        return False

    def _setup_capture_pipeline(self):
        """
        Setup asynchronous capture pipeline with RAM Disk staging
        """
        try:
            # Create staging folder
            if self.use_ram_disk:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                self._staging_folder = os.path.join(self.ram_disk_root, f"staging_{timestamp}")
                os.makedirs(self._staging_folder, exist_ok=True)
                self.log_message.emit(f"✓ RAM Disk staging enabled: {self._staging_folder}")
            else:
                self._staging_folder = self.output_folder
                self.log_message.emit("⚠️ RAM Disk not available, using direct save")

            # Setup queue and worker thread
            self._save_queue = Queue(maxsize=50)  # Buffer up to 50 images
            self._writer_stop_event = Event()
            self._files_staged = []

            # Start background writer thread
            self._writer_thread = Thread(target=self._writer_loop, daemon=True)
            self._writer_thread.start()

            self.log_message.emit("✓ Asynchronous capture pipeline initialized")
            return True

        except Exception as e:
            self.log_message.emit(f"Error setting up capture pipeline: {e}")
            return False

    def _writer_loop(self):
        """
        Background thread loop for writing images to staging area
        """
        while not self._writer_stop_event.is_set():
            try:
                # Get item from queue with timeout
                try:
                    item = self._save_queue.get(timeout=1.0)
                except Empty:
                    continue

                if item is None:  # Poison pill to stop
                    break

                frame, filename, run_number, x, y, z = item

                # Determine staging filename and path
                if self.use_ram_disk:
                    if self.staging_format == "BMP":
                        staging_filename = os.path.splitext(filename)[0] + ".bmp"
                    else:  # PNG_FAST
                        staging_filename = filename
                    staging_path = os.path.join(self._staging_folder, staging_filename)
                else:
                    staging_path = os.path.join(self.output_folder, filename)

                # Save to staging area
                success = self._save_frame_to_staging(frame, staging_path)

                if success:
                    # Track staged file for final copy
                    if self.use_ram_disk:
                        self._files_staged.append((staging_path, filename))
                else:
                    self.log_message.emit(f"⚠️ Failed to stage: {filename}")

                self._save_queue.task_done()

            except Exception as e:
                self.log_message.emit(f"Error in writer loop: {e}")
                if not self._save_queue.empty():
                    self._save_queue.task_done()

    def _save_frame_to_staging(self, frame, staging_path):
        """
        Save frame to staging area with optimized format

        Args:
            frame: numpy array (H, W, 3) RGB
            staging_path: full path to staging file

        Returns:
            bool: True if successful
        """
        try:
            height, width, channels = frame.shape
            if channels != 3:
                return False

            # Create QImage
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                return False

            # Save with optimized settings
            if self.staging_format == "BMP":
                # BMP is fastest - no compression
                success = q_image.save(staging_path, "BMP")
            else:  # PNG_FAST
                # PNG with minimal compression for speed
                writer = QImageWriter(staging_path, b"PNG")
                writer.setCompression(1)  # Minimal compression for speed
                success = writer.write(q_image)

            return success and os.path.exists(staging_path) and os.path.getsize(staging_path) > 1000

        except Exception as e:
            self.log_message.emit(f"Error saving frame to staging: {e}")
            return False

    def _finalize_capture_pipeline(self):
        """
        Finalize capture pipeline: wait for queue to empty, copy files, cleanup
        """
        try:
            finalize_start_time = time.time()

            # Signal writer thread to stop and wait for queue to empty
            if self._save_queue is not None:
                self.log_message.emit("Waiting for capture queue to finish...")
                self._save_queue.join()  # Wait for all items to be processed

                # Stop writer thread
                self._save_queue.put(None)  # Poison pill
                self._writer_stop_event.set()

                if self._writer_thread and self._writer_thread.is_alive():
                    self._writer_thread.join(timeout=10)

            # Copy files from staging to final destination
            if self.use_ram_disk and self._files_staged:
                self.log_message.emit(f"Copying {len(self._files_staged)} files from RAM Disk to final destination...")
                copy_start_time = time.time()

                for staging_path, final_filename in self._files_staged:
                    try:
                        final_path = os.path.join(self.output_folder, final_filename)

                        if self.staging_format == "BMP" and self.final_format == "PNG":
                            # Convert BMP to PNG during copy
                            self._convert_bmp_to_png(staging_path, final_path)
                        else:
                            # Direct copy
                            shutil.copy2(staging_path, final_path)

                        # Verify final file
                        if os.path.exists(final_path) and os.path.getsize(final_path) > 1000:
                            pass  # Success
                        else:
                            self.log_message.emit(f"⚠️ Final file verification failed: {final_filename}")

                    except Exception as e:
                        self.log_message.emit(f"Error copying {final_filename}: {e}")

                copy_time = time.time() - copy_start_time
                self.log_message.emit(f"✓ File copy completed in {copy_time:.2f} seconds")

                # Cleanup staging folder
                try:
                    shutil.rmtree(self._staging_folder)
                    self.log_message.emit("✓ RAM Disk staging folder cleaned up")
                except Exception as e:
                    self.log_message.emit(f"Warning: Could not cleanup staging folder: {e}")

            finalize_time = time.time() - finalize_start_time
            self.timing_stats['finalize_time'] = finalize_time
            self.log_message.emit(f"✓ Capture pipeline finalized in {finalize_time:.2f} seconds")

        except Exception as e:
            self.log_message.emit(f"Error finalizing capture pipeline: {e}")

    def _convert_bmp_to_png(self, bmp_path, png_path):
        """
        Convert BMP file to PNG with high quality compression

        Args:
            bmp_path: source BMP file path
            png_path: destination PNG file path
        """
        try:
            # Load BMP and save as PNG
            q_image = QImage(bmp_path)
            if not q_image.isNull():
                writer = QImageWriter(png_path, b"PNG")
                writer.setCompression(6)  # Good compression for final files
                return writer.write(q_image)
            return False
        except Exception as e:
            self.log_message.emit(f"Error converting BMP to PNG: {e}")
            return False

    def _cleanup_capture_pipeline(self):
        """
        Emergency cleanup of capture pipeline resources
        """
        try:
            if self._writer_stop_event:
                self._writer_stop_event.set()

            if self._writer_thread and self._writer_thread.is_alive():
                self._writer_thread.join(timeout=5)

            if self.use_ram_disk and self._staging_folder and os.path.exists(self._staging_folder):
                try:
                    shutil.rmtree(self._staging_folder)
                except Exception:
                    pass  # Best effort cleanup

        except Exception:
            pass  # Silent cleanup

    def prepare_stitching(self):
        """
        Prepare stitching by getting all interpolated grid points and creating output folder

        Returns:
            bool: True if preparation successful, False otherwise
        """
        try:
            # Safety check: Validate camera connection
            if self.camera is None:
                self.error_occurred.emit("Camera not available")
                return False

            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running")
                return False

            # Safety check: Validate GRBL connection
            if self.grbl is None:
                self.error_occurred.emit("GRBL not available")
                return False

            if not hasattr(self.grbl, 'grbl') or not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL is not connected")
                return False

            # Get all interpolated points
            if self.z_interpolation is None:
                self.error_occurred.emit("Z interpolation data not available")
                return False

            all_points = self.z_interpolation.get_interpolated_grid_points()
            if not all_points:
                self.error_occurred.emit("No interpolated points available")
                return False

            # Safety check: Validate interpolated points
            if len(all_points) < 4:
                self.error_occurred.emit(f"Too few interpolated points ({len(all_points)}). Need at least 4 points.")
                return False

            # Prepare interpolated Z grid points (Run 1)
            self.interpolated_grid_points = self._create_efficient_path(all_points)

            # Prepare fixed Z grid points (Run 2) - same XY positions but with center Z
            if self.center_z_value is not None:
                fixed_z_points = {}
                for (x, y), _ in all_points.items():
                    fixed_z_points[(x, y)] = self.center_z_value
                self.fixed_z_grid_points = self._create_efficient_path(fixed_z_points)
                self.log_message.emit(f"✓ Prepared dual stitching: {len(self.interpolated_grid_points)} points with interpolated Z + {len(self.fixed_z_grid_points)} points with fixed Z={self.center_z_value:.4f}")
            else:
                self.log_message.emit("⚠️ Warning: Center Z not available, will only run interpolated stitching")
                self.total_runs = 1
                self.fixed_z_grid_points = []

            # Set initial grid points for first run
            self.grid_points = self.interpolated_grid_points
            self.total_points = len(self.grid_points)

            # Safety check: Validate coordinate ranges
            if not self._validate_coordinate_ranges():
                return False

            # Validate path distances once at the beginning
            self._validate_path_distances()

            self.log_message.emit(f"Prepared {self.total_points} points for stitching (Run {self.current_run}/{self.total_runs})")

            # Create output folder
            if not self._create_output_folder():
                return False

            # Setup optimized capture pipeline
            if not self._setup_capture_pipeline():
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error preparing stitching: {e}")
            return False
    
    def _create_efficient_path(self, points_dict):
        """
        Create an efficient movement path through all grid points
        Uses zigzag pattern to minimize travel distance
        
        Args:
            points_dict: Dictionary with (x,y) keys and z values
            
        Returns:
            list: List of (x, y, z) tuples in optimal order
        """
        # Convert to list of tuples
        points_list = [(x, y, z) for (x, y), z in points_dict.items()]
        
        # Group by Y coordinate and sort
        y_groups = {}
        for x, y, z in points_list:
            if y not in y_groups:
                y_groups[y] = []
            y_groups[y].append((x, y, z))
        
        # Sort Y coordinates
        sorted_y = sorted(y_groups.keys())
        
        # Create zigzag pattern
        efficient_path = []
        reverse = False
        
        for y in sorted_y:
            row_points = sorted(y_groups[y], key=lambda p: p[0])  # Sort by X
            
            if reverse:
                row_points.reverse()
            
            efficient_path.extend(row_points)
            reverse = not reverse  # Alternate direction for next row
        
        self.log_message.emit(f"Created efficient zigzag path with {len(efficient_path)} points")
        return efficient_path

    def _validate_coordinate_ranges(self):
        """
        Validate that all coordinates are within safe ranges

        Returns:
            bool: True if all coordinates are safe, False otherwise
        """
        try:
            # Define safety limits (adjust based on your machine)
            X_MIN, X_MAX = -5.0, 50.0   # mm
            Y_MIN, Y_MAX = -5.0, 50.0   # mm
            Z_MIN, Z_MAX = 0.0, 25.0    # mm

            for x, y, z in self.grid_points:
                if not (X_MIN <= x <= X_MAX):
                    self.error_occurred.emit(f"X coordinate {x:.3f} is outside safe range [{X_MIN}, {X_MAX}]")
                    return False

                if not (Y_MIN <= y <= Y_MAX):
                    self.error_occurred.emit(f"Y coordinate {y:.3f} is outside safe range [{Y_MIN}, {Y_MAX}]")
                    return False

                if not (Z_MIN <= z <= Z_MAX):
                    self.error_occurred.emit(f"Z coordinate {z:.4f} is outside safe range [{Z_MIN}, {Z_MAX}]")
                    return False

            self.log_message.emit("All coordinates are within safe ranges")
            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating coordinates: {e}")
            return False

    def _validate_path_distances(self):
        """
        Validate that the distance between consecutive points is within a safe limit.
        This is a check on the generated path, not real-time movement.
        """
        max_step_distance = 10.0  # mm
        if len(self.grid_points) < 2:
            return  # Not enough points to check distance

        for i in range(len(self.grid_points) - 1):
            p1 = self.grid_points[i]
            p2 = self.grid_points[i+1]
            
            distance = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)**0.5
            
            if distance > max_step_distance:
                self.log_message.emit(
                    f"Warning: Large distance between point {i} ({p1[0]:.3f}, {p1[1]:.3f}) "
                    f"and point {i+1} ({p2[0]:.3f}, {p2[1]:.3f}). Distance: {distance:.3f}mm"
                )
        self.log_message.emit("Path distance validation complete.")

    def _create_output_folder(self):
        """
        Create output folder for stitching images
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.output_folder is None:
                # Create folder with timestamp and dual stitching indicator
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if self.total_runs > 1:
                    self.output_folder = os.path.join("Software", "Stitching", f"dual_stitching_{timestamp}")
                else:
                    self.output_folder = os.path.join("Software", "Stitching", f"stitching_{timestamp}")

            os.makedirs(self.output_folder, exist_ok=True)

            # Create info file about the dual stitching process
            if self.total_runs > 1:
                info_file = os.path.join(self.output_folder, "stitching_info.txt")
                with open(info_file, 'w') as f:
                    f.write("DUAL STITCHING PROCESS\n")
                    f.write("="*50 + "\n")
                    f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Total Runs: {self.total_runs}\n")
                    f.write(f"Run 1: Interpolated Z values from mapping AF\n")
                    if self.center_z_value is not None:
                        f.write(f"Run 2: Fixed Z value = {self.center_z_value:.4f} (center point from mapping AF)\n")
                    f.write("\nFile Naming Convention:\n")
                    f.write("stitch_r1_interp_p###_X###.###_Y###.###_Z##.####_HHMMSS.png - Interpolated Z\n")
                    f.write("stitch_r2_fixed_p###_X###.###_Y###.###_Z##.####_HHMMSS.png - Fixed Z\n")
                    f.write("="*50 + "\n")

            self.log_message.emit(f"Output folder created: {self.output_folder}")
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Error creating output folder: {e}")
            return False
    
    def run(self):
        """
        Main stitching process
        """
        try:
            self.is_running = True
            self.should_stop = False
            self.timing_stats = {
                'start_time': time.time(),
                'preparation_time': 0,
                'total_movement_time': 0,
                'total_capture_time': 0,
                'total_time': 0
            }
            
            self.log_message.emit("Starting stitching process...")
            
            # Prepare stitching
            prep_start_time = time.time()
            if not self.prepare_stitching():
                self.finished.emit()
                return
            self.timing_stats['preparation_time'] = time.time() - prep_start_time
            
            # Disable GRBL polling for stable operation
            if hasattr(self.grbl, 'stop_polling'):
                self.log_message.emit("[STITCHING] Disabling GRBL polling for stable operation")
                self.grbl.stop_polling()

            # Run dual stitching process
            for run_number in range(1, self.total_runs + 1):
                if not self._validate_system_state():
                    break

                self.current_run = run_number

                # Set appropriate grid points for current run
                if run_number == 1:
                    self.grid_points = self.interpolated_grid_points
                    run_description = "Interpolated Z"
                    self.log_message.emit(f"\n{'='*60}")
                    self.log_message.emit(f"STARTING RUN {run_number}/{self.total_runs}: STITCHING WITH INTERPOLATED Z")
                    self.log_message.emit(f"{'='*60}")
                elif run_number == 2:
                    self.grid_points = self.fixed_z_grid_points
                    run_description = f"Fixed Z={self.center_z_value:.4f}"
                    self.log_message.emit(f"\n{'='*60}")
                    self.log_message.emit(f"STARTING RUN {run_number}/{self.total_runs}: STITCHING WITH FIXED Z={self.center_z_value:.4f}")
                    self.log_message.emit(f"{'='*60}")

                self.total_points = len(self.grid_points)

                # Process each point in current run
                for i, (x, y, z) in enumerate(self.grid_points):
                    if not self._validate_system_state():
                        break

                    self.current_point = i
                    # Calculate overall progress across both runs
                    overall_progress = int(((run_number - 1) * len(self.grid_points) + i) / (self.total_runs * len(self.grid_points)) * 100)
                    self.progress.emit(overall_progress, f"Run {run_number}/{self.total_runs} ({run_description}): Point {i+1}/{self.total_points}")
                    self.log_message.emit(f"[Run {run_number}] Moving to point {i+1}/{self.total_points}: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")

                    # Move to position
                    move_start_time = time.time()
                    if not self._move_to_position(x, y, z):
                        self.error_occurred.emit(f"Failed to move to position ({x:.3f}, {y:.3f}, {z:.4f})")
                        self._emergency_stop()
                        break
                    self.timing_stats['total_movement_time'] += time.time() - move_start_time

                    if not self._validate_system_state():
                        break

                    # Capture and save image with run-specific naming
                    capture_start_time = time.time()
                    if not self._capture_and_save_image(x, y, z, i, run_number):
                        self.error_occurred.emit(f"Failed to capture image at position ({x:.3f}, {y:.3f}, {z:.4f})")
                        continue
                    self.timing_stats['total_capture_time'] += time.time() - capture_start_time

                if not self.should_stop:
                    self.log_message.emit(f"✓ Run {run_number}/{self.total_runs} completed successfully ({run_description})")

            if not self.should_stop:
                self.progress.emit(100, "Dual stitching completed successfully")
                self.log_message.emit(f"\n{'='*60}")
                self.log_message.emit("DUAL STITCHING PROCESS COMPLETED SUCCESSFULLY")
                self.log_message.emit(f"{'='*60}")
                self.log_message.emit(f"✓ Run 1: Stitching with interpolated Z values")
                if self.total_runs > 1:
                    self.log_message.emit(f"✓ Run 2: Stitching with fixed Z={self.center_z_value:.4f}")
                self.log_message.emit(f"{'='*60}")
            
        except Exception as e:
            self.error_occurred.emit(f"Error during stitching: {e}")
        finally:
            # Finalize capture pipeline (copy files from RAM Disk, cleanup)
            try:
                self._finalize_capture_pipeline()
            except Exception as e:
                self.log_message.emit(f"Error during pipeline finalization: {e}")

            self.timing_stats['total_time'] = time.time() - self.timing_stats['start_time']
            self._log_timing_summary()

            # Re-enable GRBL polling
            if hasattr(self.grbl, 'start_polling'):
                self.log_message.emit("[STITCHING] Re-enabling GRBL polling")
                self.grbl.start_polling()

            # Emergency cleanup
            self._cleanup_capture_pipeline()

            self.is_running = False
            self.finished.emit()
    
    def _move_to_position(self, x, y, z):
        """
        Move to specified XYZ position with safety checks

        Args:
            x, y, z: Target coordinates

        Returns:
            bool: True if movement successful
        """
        try:
            # Safety check: Verify GRBL is still connected
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost during movement")
                return False

            # Move XY first with timeout
            self.log_message.emit(f"Moving XY to ({x:.3f}, {y:.3f})")
            self.grbl.move_to(x, y)

            if not self.grbl.wait_for_idle(timeout_seconds=30):
                self.error_occurred.emit("XY movement timeout - stopping for safety")
                return False

            # Verify XY position reached (optional for testing)
            try:
                new_x, new_y, _ = self.grbl.get_current_position()
                xy_tolerance = 0.5  # mm - more lenient tolerance
                distance_error = ((new_x - x)**2 + (new_y - y)**2)**0.5

                if distance_error > xy_tolerance:
                    self.log_message.emit(f"XY position warning: Expected ({x:.3f}, {y:.3f}), got ({new_x:.3f}, {new_y:.3f}), error: {distance_error:.3f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"XY position verified: ({new_x:.3f}, {new_y:.3f})")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify XY position: {e}")

            # Move Z with timeout
            self.log_message.emit(f"Moving Z to {z:.4f}")
            self.grbl.move_to_z(z)

            if not self.grbl.wait_for_idle(timeout_seconds=15):
                self.error_occurred.emit("Z movement timeout - stopping for safety")
                return False

            # Verify Z position reached (optional for testing)
            try:
                _, _, new_z = self.grbl.get_current_position()
                z_tolerance = 0.1  # mm - more lenient tolerance
                z_error = abs(new_z - z)

                if z_error > z_tolerance:
                    self.log_message.emit(f"Z position warning: Expected {z:.4f}, got {new_z:.4f}, error: {z_error:.4f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"Z position verified: {new_z:.4f}")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify Z position: {e}")

            # Wait for settling
            time.sleep(self.settle_time)

            self.log_message.emit(f"Successfully moved to ({x:.3f}, {y:.3f}, {z:.4f})")
            self.last_known_position = (x, y, z)
            return True

        except Exception as e:
            self.error_occurred.emit(f"Critical error during movement: {e}")
            return False
    
    def _capture_and_save_image(self, x, y, z, point_index, run_number=1):
        """
        Capture image from camera and queue for asynchronous saving

        Args:
            x, y, z: Current coordinates
            point_index: Index of current point
            run_number: Current run number (1 for interpolated Z, 2 for fixed Z)

        Returns:
            bool: True if capture successful
        """
        try:
            # Safety check: Verify camera is still running
            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running during capture")
                return False

            # Wait before capture for image stabilization
            time.sleep(self.capture_delay)

            # Get frame from camera
            frame = None
            if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                frame = self.camera._last_numpy_frame.copy()
            else:
                self.log_message.emit("Waiting for camera frame...")
                time.sleep(0.1)  # Short wait if frame not immediately available
                if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                    frame = self.camera._last_numpy_frame.copy()

            if frame is None:
                self.error_occurred.emit("No frame available from camera")
                return False

            # Validate frame dimensions
            if len(frame.shape) != 3:
                self.error_occurred.emit(f"Invalid frame shape: {frame.shape}")
                return False

            height, width, channels = frame.shape
            if channels != 3:
                self.error_occurred.emit(f"Expected 3 channels, got {channels}")
                return False

            # Create filename with coordinates, run number, and timestamp
            timestamp = datetime.now().strftime("%H%M%S")
            run_suffix = "interp" if run_number == 1 else "fixed"
            filename = f"stitch_r{run_number}_{run_suffix}_p{point_index:03d}_X{x:.3f}_Y{y:.3f}_Z{z:.4f}_{timestamp}.png"

            # Queue frame for asynchronous saving (FAST - no I/O blocking)
            if self._save_queue is not None:
                try:
                    # Put item in queue - this should be very fast
                    item = (frame.copy(), filename, run_number, x, y, z)
                    self._save_queue.put(item, timeout=1.0)  # 1 second timeout

                    # Log immediate success (actual save happens in background)
                    self.log_message.emit(f"✓ Queued for save: {filename}")
                    return True

                except Exception as e:
                    self.error_occurred.emit(f"Failed to queue image for saving: {e}")
                    return False
            else:
                # Fallback to synchronous save if pipeline not available
                return self._save_image_synchronous(frame, filename, x, y, z)

        except Exception as e:
            self.error_occurred.emit(f"Critical error capturing image: {e}")
            return False

    def _save_image_synchronous(self, frame, filename, x, y, z):
        """
        Fallback synchronous save method (used when pipeline not available)

        Args:
            frame: numpy array (H, W, 3) RGB
            filename: target filename
            x, y, z: coordinates for logging

        Returns:
            bool: True if successful
        """
        try:
            height, width, channels = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                self.error_occurred.emit("Failed to create QImage from frame")
                return False

            filepath = os.path.join(self.output_folder, filename)
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # Save image with error checking
            if q_image.save(filepath, "PNG"):
                # Verify file was actually created and has reasonable size
                if os.path.exists(filepath) and os.path.getsize(filepath) > 1000:  # At least 1KB
                    self.log_message.emit(f"✓ Saved (sync): {filename} ({os.path.getsize(filepath)} bytes)")
                    return True
                else:
                    self.error_occurred.emit(f"File saved but appears corrupted: {filename}")
                    return False
            else:
                self.error_occurred.emit(f"Failed to save image: {filename}")
                return False

        except Exception as e:
            self.error_occurred.emit(f"Error in synchronous save: {e}")
            return False
    
    def stop(self):
        """
        Stop the stitching process safely
        """
        self.should_stop = True
        self.log_message.emit("Emergency stop requested - stopping stitching process...")

        # Emergency stop GRBL movement
        try:
            if self.grbl and hasattr(self.grbl, 'stop_jog'):
                self.grbl.stop_jog()
                self.log_message.emit("GRBL movement stopped")
        except Exception as e:
            self.log_message.emit(f"Error stopping GRBL: {e}")

        # Stop capture pipeline
        try:
            self._cleanup_capture_pipeline()
        except Exception as e:
            self.log_message.emit(f"Error stopping capture pipeline: {e}")

    def _emergency_stop(self):
        """
        Emergency stop function for critical situations
        """
        try:
            self.should_stop = True
            self.error_occurred.emit("EMERGENCY STOP ACTIVATED")

            # Stop all GRBL movement immediately
            if self.grbl and hasattr(self.grbl, 'grbl') and self.grbl.grbl.is_open:
                # Send emergency stop command
                self.grbl.grbl.write(b'\x85')  # Real-time command for jog cancel
                self.grbl.grbl.write(b'!')     # Feed hold
                self.log_message.emit("Emergency stop commands sent to GRBL")

        except Exception as e:
            self.log_message.emit(f"Error during emergency stop: {e}")

    def _validate_system_state(self):
        """
        Validate system state before continuing with next point

        Returns:
            bool: True if system is in good state, False otherwise
        """
        try:
            # Check GRBL connection
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost")
                return False

            # Check camera state
            if not self.camera.is_running:
                self.error_occurred.emit("Camera stopped running")
                return False

            # Check if stop was requested
            if self.should_stop:
                self.log_message.emit("Stop requested by user")
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating system state: {e}")
            return False

    def _log_timing_summary(self):
        """
        Log a summary of the timing statistics for the stitching process.
        """
        if self.total_runs > 1:
            self.log_message.emit("--- DUAL STITCHING TIMING SUMMARY ---")
            self.log_message.emit(f"Total Runs Completed: {self.total_runs}")
            self.log_message.emit(f"Run 1: Interpolated Z values")
            if self.center_z_value is not None:
                self.log_message.emit(f"Run 2: Fixed Z = {self.center_z_value:.4f}")
        else:
            self.log_message.emit("--- Stitching Timing Summary ---")

        self.log_message.emit(f"Preparation Time: {self.timing_stats.get('preparation_time', 0):.2f} seconds")
        self.log_message.emit(f"Total Movement Time: {self.timing_stats.get('total_movement_time', 0):.2f} seconds")
        self.log_message.emit(f"Total Capture Time: {self.timing_stats.get('total_capture_time', 0):.2f} seconds")
        finalize_time = self.timing_stats.get('finalize_time', 0)
        if finalize_time > 0:
            self.log_message.emit(f"Pipeline Finalize Time: {finalize_time:.2f} seconds")
        self.log_message.emit(f"Total Stitching Time: {self.timing_stats.get('total_time', 0):.2f} seconds")

        # Show optimization info
        if self.use_ram_disk:
            self.log_message.emit(f"✓ RAM Disk optimization enabled (staging format: {self.staging_format})")
        else:
            self.log_message.emit("⚠️ RAM Disk optimization disabled (direct save)")

        if self.total_runs > 1:
            total_points = len(self.interpolated_grid_points) * self.total_runs
            self.log_message.emit(f"Total Images Captured: {total_points} ({len(self.interpolated_grid_points)} per run × {self.total_runs} runs)")

        self.log_message.emit("-" * (50 if self.total_runs > 1 else 33))
