"""
Test script for optimized stitching with RAM Disk
"""

import os
import sys
import time
import numpy as np
from datetime import datetime

# Add the Software directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Software'))

def test_ram_disk_detection():
    """Test RAM Disk detection"""
    print("Testing RAM Disk detection...")
    
    from Stitching.StitchingWorker import StitchingWorker
    
    # Create a mock worker to test detection
    worker = StitchingWorker(None, None, None)
    
    print(f"RAM Disk detected: {worker.use_ram_disk}")
    print(f"RAM Disk path: {worker.ram_disk_root}")
    print(f"Staging format: {worker.staging_format}")
    print(f"Final format: {worker.final_format}")
    
    return worker.use_ram_disk

def test_staging_folder_creation():
    """Test staging folder creation"""
    print("\nTesting staging folder creation...")
    
    from Stitching.StitchingWorker import StitchingWorker
    
    worker = <PERSON>itchingWorker(None, None, None)
    
    if worker.use_ram_disk:
        # Test staging folder creation
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        staging_folder = os.path.join(worker.ram_disk_root, f"test_staging_{timestamp}")
        
        try:
            os.makedirs(staging_folder, exist_ok=True)
            print(f"✓ Successfully created staging folder: {staging_folder}")
            
            # Test write capability
            test_file = os.path.join(staging_folder, "test_write.txt")
            with open(test_file, 'w') as f:
                f.write("Test write to RAM Disk")
            
            print(f"✓ Successfully wrote test file: {test_file}")
            
            # Cleanup
            os.remove(test_file)
            os.rmdir(staging_folder)
            print("✓ Cleanup successful")
            
            return True
            
        except Exception as e:
            print(f"✗ Error testing staging folder: {e}")
            return False
    else:
        print("⚠️ RAM Disk not available, skipping staging test")
        return False

def test_image_save_performance():
    """Test image save performance comparison"""
    print("\nTesting image save performance...")
    
    # Create test image data
    width, height = 1920, 1080
    test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    from PyQt5.QtGui import QImage, QImageWriter
    
    # Test different save methods
    results = {}
    
    # Test 1: PNG with default compression (current method)
    print("Testing PNG default compression...")
    start_time = time.time()
    for i in range(5):
        bytes_per_line = 3 * width
        q_image = QImage(test_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
        temp_path = f"temp_png_default_{i}.png"
        q_image.save(temp_path, "PNG")
        try:
            if os.path.exists(temp_path):
                time.sleep(0.1)
                os.remove(temp_path)
        except:
            pass
    results['PNG_default'] = (time.time() - start_time) / 5
    
    # Test 2: PNG with minimal compression
    print("Testing PNG minimal compression...")
    start_time = time.time()
    for i in range(5):
        bytes_per_line = 3 * width
        q_image = QImage(test_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
        temp_path = f"temp_png_fast_{i}.png"
        writer = QImageWriter(temp_path, b"PNG")
        writer.setCompression(1)
        writer.write(q_image)
        try:
            if os.path.exists(temp_path):
                time.sleep(0.1)  # Small delay to ensure file is released
                os.remove(temp_path)
        except:
            pass  # Ignore cleanup errors in test
    results['PNG_fast'] = (time.time() - start_time) / 5
    
    # Test 3: BMP (no compression)
    print("Testing BMP (no compression)...")
    start_time = time.time()
    for i in range(5):
        bytes_per_line = 3 * width
        q_image = QImage(test_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
        temp_path = f"temp_bmp_{i}.bmp"
        q_image.save(temp_path, "BMP")
        try:
            if os.path.exists(temp_path):
                time.sleep(0.1)
                os.remove(temp_path)
        except:
            pass
    results['BMP'] = (time.time() - start_time) / 5
    
    # Print results
    print("\nPerformance Results (average time per save):")
    for method, avg_time in results.items():
        print(f"  {method}: {avg_time:.3f} seconds")
    
    # Calculate speedup
    if 'PNG_default' in results and 'BMP' in results:
        speedup = results['PNG_default'] / results['BMP']
        print(f"\nBMP speedup vs PNG default: {speedup:.1f}x faster")
    
    return results

def main():
    """Main test function"""
    print("=" * 60)
    print("OPTIMIZED STITCHING PERFORMANCE TEST")
    print("=" * 60)
    
    # Test 1: RAM Disk detection
    ram_disk_available = test_ram_disk_detection()
    
    # Test 2: Staging folder creation
    if ram_disk_available:
        staging_ok = test_staging_folder_creation()
    else:
        staging_ok = False
    
    # Test 3: Image save performance
    performance_results = test_image_save_performance()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"RAM Disk Available: {'✓' if ram_disk_available else '✗'}")
    print(f"Staging Test: {'✓' if staging_ok else '✗'}")
    print(f"Performance Test: {'✓' if performance_results else '✗'}")
    
    if ram_disk_available and performance_results:
        print("\n✓ Optimized stitching should provide significant speedup!")
        print("  - Images will be staged to RAM Disk for ultra-fast writes")
        print("  - Background thread will handle encoding/compression")
        print("  - Final files copied to project folder after completion")
    else:
        print("\n⚠️ Optimized stitching will use fallback mode")
        print("  - Direct save to project folder (slower)")
        print("  - Consider setting up RAM Disk on A: drive for best performance")

if __name__ == "__main__":
    main()
